<?php
/**
 * Plugin Name: Awesome Reports
 * Plugin URI: https://awesomereports.com/
 * Description: Display update statistics directly in the WordPress admin or send reports via email.
 * Version: 1.0.0
 * Author: Bohemia Plugins
 * Author URI: https://bohemiaplugins.com/
 * Text Domain: awesome-reports
 * Domain Path: /languages/
 * 
 * Awesome Reports, 
 * Copyright (C) 2025, Bohemia Plugins, <EMAIL>
 * 
 * Awesome Reports is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * any later version.
 *
 * Awesome Reports is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Awesome Reports. If not, see <https://www.gnu.org/licenses/>.
 * 
 * Awesome Reports incorporates code from WordPress plugin "WP Client Reports"
 * <https://wordpress.org/plugins/wp-client-reports/> by <PERSON> <https://mikegillihan.com/>.
 * License: GNU GPL, Version 2
*/

if (!defined('ABSPATH')) {
    exit;
}

/**
 * On plugin activation create the database tables needed to store updates
 */
function awesome_reports_data_install() {
	global $wpdb;
	global $wp_client_reports_version;

	$wp_client_reports_table_name = $wpdb->prefix . 'awesome_reports';

	$charset_collate = $wpdb->get_charset_collate();

	$wp_client_reports_sql = "CREATE TABLE $wp_client_reports_table_name (
		id mediumint(9) NOT NULL AUTO_INCREMENT,
		date date DEFAULT '0000-00-00' NOT NULL,
        type varchar(191),
        name varchar(191),
        slug varchar(191),
        version_before varchar(191),
        version_after varchar(191),
        active tinyint(1),
		UNIQUE KEY id (id)
	) $charset_collate;";

	require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
	dbDelta( $wp_client_reports_sql );

	add_option( 'wp_client_reports_version', WP_CLIENT_REPORTS_VERSION );
	add_option( 'wp_client_reports_enable_updates', 'on' );
	add_option( 'wp_client_reports_enable_content_stats', 'on' );

	wp_client_reports_check_for_updates();
}

add_action( 'init', 'wp_client_reports_load_actions', 985 );