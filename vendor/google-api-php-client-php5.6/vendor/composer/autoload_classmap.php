<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'ArithmeticError' => $vendorDir . '/symfony/polyfill-php70/Resources/stubs/ArithmeticError.php',
    'AssertionError' => $vendorDir . '/symfony/polyfill-php70/Resources/stubs/AssertionError.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'DivisionByZeroError' => $vendorDir . '/symfony/polyfill-php70/Resources/stubs/DivisionByZeroError.php',
    'Error' => $vendorDir . '/symfony/polyfill-php70/Resources/stubs/Error.php',
    'Google_AccessToken_Revoke' => $baseDir . '/src/aliases.php',
    'Google_AccessToken_Verify' => $baseDir . '/src/aliases.php',
    'Google_AuthHandler_AuthHandlerFactory' => $baseDir . '/src/aliases.php',
    'Google_AuthHandler_Guzzle5AuthHandler' => $baseDir . '/src/aliases.php',
    'Google_AuthHandler_Guzzle6AuthHandler' => $baseDir . '/src/aliases.php',
    'Google_AuthHandler_Guzzle7AuthHandler' => $baseDir . '/src/aliases.php',
    'Google_Client' => $baseDir . '/src/aliases.php',
    'Google_Collection' => $baseDir . '/src/aliases.php',
    'Google_Exception' => $baseDir . '/src/aliases.php',
    'Google_Http_Batch' => $baseDir . '/src/aliases.php',
    'Google_Http_MediaFileUpload' => $baseDir . '/src/aliases.php',
    'Google_Http_REST' => $baseDir . '/src/aliases.php',
    'Google_Model' => $baseDir . '/src/aliases.php',
    'Google_Service' => $baseDir . '/src/aliases.php',
    'Google_Service_Exception' => $baseDir . '/src/aliases.php',
    'Google_Service_Resource' => $baseDir . '/src/aliases.php',
    'Google_Task_Composer' => $baseDir . '/src/aliases.php',
    'Google_Task_Exception' => $baseDir . '/src/aliases.php',
    'Google_Task_Retryable' => $baseDir . '/src/aliases.php',
    'Google_Task_Runner' => $baseDir . '/src/aliases.php',
    'Google_Utils_UriTemplate' => $baseDir . '/src/aliases.php',
    'Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'ParseError' => $vendorDir . '/symfony/polyfill-php70/Resources/stubs/ParseError.php',
    'SessionUpdateTimestampHandlerInterface' => $vendorDir . '/symfony/polyfill-php70/Resources/stubs/SessionUpdateTimestampHandlerInterface.php',
    'TypeError' => $vendorDir . '/symfony/polyfill-php70/Resources/stubs/TypeError.php',
);
